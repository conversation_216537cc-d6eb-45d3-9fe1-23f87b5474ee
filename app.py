from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta, timezone
import os
import qrcode
import pandas as pd
import json
import logging
import pymysql
from config import Config

# Install MySQL driver before other imports
pymysql.install_as_MySQLdb()

# Import required Flask extensions
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_required, current_user, login_user, logout_user
from flask_socketio import SocketIO, emit
import uuid
import ast
import re

# Initialize Flask app first
app = Flask(__name__)
app.config.from_object(Config)

# Ensure upload directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['QR_CODE_FOLDER'], exist_ok=True)

# Import extensions
from extensions import db, login_manager, socketio

# Initialize extensions with app
db.init_app(app)
login_manager.init_app(app)
socketio.init_app(app)

# Import models
from models.user import User
from models.file import File
from models.location import Location, Rack, Bundle
from models.access_log import AccessLog

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# API endpoints for real-time updates
@app.route('/api/stats')
@login_required
def api_stats():
    """API endpoint to get dashboard statistics for real-time updates"""
    files = File.query.all()
    total_files = len(files)
    files_with_access_logs = len([f for f in files if f.access_logs.count() > 0])
    files_with_qr_codes = len([f for f in files if f.qr_code])
    files_from_excel = len([f for f in files if not f.filename])
    files_uploaded = len([f for f in files if f.filename])

    stats = {
        'total_files': total_files,
        'processed_files': files_with_access_logs,
        'qr_codes_generated': files_with_qr_codes,
        'excel_imports': files_from_excel,
        'manual_uploads': files_uploaded
    }
    return jsonify(stats)

@app.route('/api/recent-files')
@login_required
def api_recent_files():
    """API endpoint to get recent files for real-time updates"""
    files = File.query.order_by(File.created_at.desc()).limit(5).all()
    files_data = []
    
    for file in files:
        file_data = {
            'id': file.id,
            'title': file.title,
            'created_at': file.created_at.isoformat(),
            'location': {
                'rack_number': file.location.rack_number,
                'row_number': file.location.row_number,
                'position': file.location.position
            }
        }
        files_data.append(file_data)
    
    return jsonify({'files': files_data})

# Socket.IO event handlers
@socketio.on('excel_upload_complete')
def handle_excel_upload(data):
    """Socket.IO event handler for Excel upload completion"""
    emit('excel_upload_complete', {
        'message': 'Excel file processed successfully',
        'count': data.get('count', 0),
        'user': current_user.username
    }, broadcast=True)

@socketio.on('files_updated')
def handle_files_update(data):
    """Socket.IO event handler for file updates"""
    emit('files_updated', {
        'message': 'Files updated',
        'type': data.get('type', 'update'),
        'user': current_user.username
    }, broadcast=True)

# Routes
@app.route('/')
def index():
    return render_template('base.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('dashboard'))

        flash('Invalid username or password')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Show all files in the system, not just user-specific files
    # This ensures Excel uploads appear for all users
    files = File.query.order_by(File.created_at.desc()).all()

    # Calculate stats for dashboard (all files in system)
    total_files = len(files)
    files_with_access_logs = len([f for f in files if f.access_logs.count() > 0])
    files_with_qr_codes = len([f for f in files if f.qr_code])

    # Additional stats for better dashboard insights
    files_from_excel = len([f for f in files if not f.filename])  # Excel imports have no filename
    files_uploaded = len([f for f in files if f.filename])  # Manual uploads have filename

    stats = {
        'total_files': total_files,
        'processed_files': files_with_access_logs,
        'qr_codes_generated': files_with_qr_codes,
        'excel_imports': files_from_excel,
        'manual_uploads': files_uploaded
    }

    return render_template('dashboard.html', files=files, stats=stats)

@app.route('/files/add', methods=['GET', 'POST'])
@login_required
def add_file():
    if request.method == 'POST':
        # Handle file upload
        if 'file' not in request.files:
            flash('No file part')
            return redirect(request.url)

        file = request.files['file']

        if file.filename == '':
            flash('No selected file')
            return redirect(request.url)

        if file:
            filename = secure_filename(file.filename)

            # Check if it's an Excel file
            if filename.endswith(('.xlsx', '.xls')):
                try:
                    # Process Excel file to extract data
                    df = pd.read_excel(file)

                    # Expected columns for Excel processing
                    required_columns = ['IndexID', 'RefID', 'FILE_NO', 'Category', 'Year', 'DisposalCat',
                                      'Createddate', 'RowNo', 'RackNo', 'RecordRoomSlNo', 'BundleNo',
                                      'ClosureDate', 'ReceiptAtRRDate', 'DestructionDate', 'Subject',
                                      'dist_name_en', 'taluk_name_en', 'hobli_name_en', 'village_name_en', 'survey_no']

                    # Check if Excel has the required columns
                    missing_columns = [col for col in required_columns if col not in df.columns]
                    if missing_columns:
                        flash(f'Excel file missing required columns: {", ".join(missing_columns)}')
                        return redirect(request.url)

                    # Process first row of Excel data (or allow user to select row)
                    row_index = int(request.form.get('excel_row_index', 0))
                    if row_index >= len(df):
                        flash('Invalid row index selected')
                        return redirect(request.url)

                    row = df.iloc[row_index]

                    # Extract data from Excel row
                    rack_number = str(row['RackNo']) if pd.notna(row['RackNo']) else '1'
                    row_number = str(row['RowNo']) if pd.notna(row['RowNo']) else '1'
                    position = str(row['RecordRoomSlNo']) if pd.notna(row['RecordRoomSlNo']) else '1'
                    file_no = str(row['FILE_NO']) if pd.notna(row['FILE_NO']) else f'FILE_{row_index+1}'

                    # Create title from Excel data
                    title_parts = []
                    if pd.notna(row['FILE_NO']):
                        title_parts.append(f"File: {row['FILE_NO']}")
                    if pd.notna(row['Subject']):
                        title_parts.append(str(row['Subject']))
                    if pd.notna(row['village_name_en']):
                        title_parts.append(f"Village: {row['village_name_en']}")

                    title = ' - '.join(title_parts) if title_parts else f'Excel Import {row_index+1}'

                    # Create description from Excel data
                    description_parts = []
                    if pd.notna(row['Category']):
                        description_parts.append(f"Category: {row['Category']}")
                    if pd.notna(row['Year']):
                        description_parts.append(f"Year: {row['Year']}")
                    if pd.notna(row['dist_name_en']):
                        description_parts.append(f"District: {row['dist_name_en']}")
                    if pd.notna(row['taluk_name_en']):
                        description_parts.append(f"Taluk: {row['taluk_name_en']}")
                    if pd.notna(row['hobli_name_en']):
                        description_parts.append(f"Hobli: {row['hobli_name_en']}")
                    if pd.notna(row['survey_no']):
                        description_parts.append(f"Survey No: {row['survey_no']}")

                    description = ' | '.join(description_parts) if description_parts else 'Imported from Excel'

                except Exception as e:
                    flash(f'Error processing Excel file: {str(e)}')
                    return redirect(request.url)
            else:
                # For non-Excel files, use form data
                title = request.form.get('title')
                description = request.form.get('description')
                rack_number = request.form.get('rack_number')
                row_number = request.form.get('row_number')
                position = request.form.get('position')

                if not all([title, rack_number, row_number, position]):
                    flash('Please fill in all required fields for non-Excel files')
                    return redirect(request.url)

            # Save the uploaded file
            unique_filename = f"{uuid.uuid4()}_{filename}"
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(file_path)

            # Create location record
            location = Location(rack_number=rack_number, row_number=row_number, position=position)
            db.session.add(location)
            db.session.flush()  # Get the location ID without committing

            # Find or create bundle for this file
            rack_num = int(rack_number) if rack_number.isdigit() else 1
            bundle_num = 1  # Default bundle for manual uploads

            # Ensure valid ranges
            if rack_num < 1 or rack_num > 20:
                rack_num = 1

            # Find the rack and bundle
            rack = Rack.query.filter_by(rack_number=rack_num).first()
            if rack:
                bundle = Bundle.query.filter_by(
                    rack_id=rack.id,
                    bundle_number=bundle_num
                ).first()
                bundle_id = bundle.id if bundle else None
            else:
                bundle_id = None

            # Create file record
            new_file = File(
                title=title,
                description=description,
                filename=unique_filename,
                original_filename=filename,
                location_id=location.id,
                user_id=current_user.id,
                bundle_id=bundle_id
            )
            db.session.add(new_file)
            db.session.flush()  # Get the file ID without committing

            # Generate QR code
            qr_data = {
                'file_id': new_file.id,
                'title': title,
                'location': f"Rack: {rack_number}, Row: {row_number}, Position: {position}",
                'url': f"http://127.0.0.1:5001/files/{new_file.id}",
                'type': 'T-Office-File'
            }

            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=10,
                border=4,
            )

            qr.add_data(json.dumps(qr_data))
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")
            qr_filename = f"qr_{new_file.id}.png"
            qr_path = os.path.join(app.config['QR_CODE_FOLDER'], qr_filename)
            qr_img.save(qr_path)

            # Update file with QR code path
            new_file.qr_code = qr_filename

            # Log the file access
            access_log = AccessLog(
                file_id=new_file.id,
                user_id=current_user.id,
                action="created"
            )
            db.session.add(access_log)

            # Commit all changes
            db.session.commit()

            flash('File added successfully')
            return redirect(url_for('view_file', file_id=new_file.id))

    return render_template('add_file.html')

@app.route('/files/<int:file_id>')
@login_required
def view_file(file_id):
    file = File.query.get_or_404(file_id)

    # Log the file access
    access_log = AccessLog(
        file_id=file.id,
        user_id=current_user.id,
        action="viewed"
    )
    db.session.add(access_log)
    db.session.commit()

    # Get access logs for this file
    access_logs = AccessLog.query.filter_by(file_id=file.id).order_by(AccessLog.timestamp.desc()).limit(10).all()

    return render_template('view_file.html', file=file, access_logs=access_logs)

@app.route('/files/<int:file_id>/download')
@login_required
def download_file(file_id):
    file = File.query.get_or_404(file_id)

    # Check if file has actual filename (not Excel import)
    if not file.filename:
        flash('This file was imported from Excel and has no downloadable file')
        return redirect(url_for('view_file', file_id=file_id))

    # Log the file access
    access_log = AccessLog(
        file_id=file.id,
        user_id=current_user.id,
        action="downloaded"
    )
    db.session.add(access_log)
    db.session.commit()

    return send_from_directory(
        app.config['UPLOAD_FOLDER'],
        file.filename,
        as_attachment=True,
        attachment_filename=file.original_filename or file.filename
    )

@app.route('/files/<int:file_id>/qrcode')
@login_required
def get_qrcode(file_id):
    file = File.query.get_or_404(file_id)

    # Check if QR code exists
    if not file.qr_code:
        flash('QR code not found for this file')
        return redirect(url_for('view_file', file_id=file_id))

    return send_from_directory(
        app.config['QR_CODE_FOLDER'],
        file.qr_code,
        as_attachment=False
    )

@app.route('/scan', methods=['GET', 'POST'])
@login_required
def scan_qrcode():
    if request.method == 'POST':
        # In a real app, you would process the QR code data here
        # For now, we'll simulate by accepting a file_id
        file_id = request.form.get('file_id')
        if file_id:
            return redirect(url_for('view_file', file_id=file_id))

    return render_template('scan.html')

@app.route('/api/scan', methods=['POST'])
def api_scan_qrcode():
    data = request.json
    qr_data = data.get('qr_data')

    if not qr_data:
        return jsonify({'success': False, 'error': 'No QR data provided'})

    try:
        file_id = None

        # Try multiple parsing methods for different QR code formats

        # Method 1: Try parsing as JSON
        try:
            import json
            qr_dict = json.loads(qr_data)
            file_id = qr_dict.get('file_id')
        except:
            pass

        # Method 2: Try parsing as Python literal (ast.literal_eval)
        if not file_id:
            try:
                import ast
                qr_dict = ast.literal_eval(qr_data)
                file_id = qr_dict.get('file_id')
            except:
                pass

        # Method 3: Try parsing as URL (e.g., http://domain.com/files/123)
        if not file_id:
            try:
                import re
                # Look for patterns like /files/123 or file_id=123
                url_match = re.search(r'/files/(\d+)', qr_data)
                if url_match:
                    file_id = int(url_match.group(1))
                else:
                    # Look for file_id parameter
                    id_match = re.search(r'file_id[=:](\d+)', qr_data)
                    if id_match:
                        file_id = int(id_match.group(1))
            except:
                pass

        # Method 4: Try parsing as plain number (direct file ID)
        if not file_id:
            try:
                file_id = int(qr_data.strip())
            except:
                pass

        # Method 5: Try extracting from T-Office specific format
        if not file_id:
            try:
                # Look for T-Office format: "T-Office-File-ID-123"
                if 'T-Office' in qr_data:
                    id_match = re.search(r'ID[:-](\d+)', qr_data)
                    if id_match:
                        file_id = int(id_match.group(1))
            except:
                pass

        if file_id:
            file = File.query.get(file_id)
            if file:
                # Log the file access
                access_log = AccessLog(
                    file_id=file.id,
                    user_id=current_user.id if current_user.is_authenticated else None,
                    action="scanned"
                )
                db.session.add(access_log)
                db.session.commit()

                return jsonify({
                    'success': True,
                    'file_id': file.id,
                    'title': file.title,
                    'location': {
                        'rack': file.location.rack_number,
                        'row': file.location.row_number,
                        'position': file.location.position
                    }
                })
            else:
                return jsonify({'success': False, 'error': f'File with ID {file_id} not found'})
        else:
            return jsonify({'success': False, 'error': f'Could not extract file ID from QR data: {qr_data[:100]}...'})

    except Exception as e:
        return jsonify({'success': False, 'error': f'Error processing QR code: {str(e)}'})

@app.route('/api/scan/debug', methods=['POST'])
def api_scan_debug():
    """Debug endpoint to help troubleshoot QR scanning issues"""
    data = request.json
    qr_data = data.get('qr_data')

    debug_info = {
        'received_data': qr_data,
        'data_type': type(qr_data).__name__,
        'data_length': len(qr_data) if qr_data else 0,
        'parsing_attempts': []
    }

    if not qr_data:
        debug_info['error'] = 'No QR data provided'
        return jsonify(debug_info)

    file_id = None

    # Method 1: Try parsing as JSON
    try:
        import json
        qr_dict = json.loads(qr_data)
        file_id = qr_dict.get('file_id')
        debug_info['parsing_attempts'].append({
            'method': 'JSON parsing',
            'success': bool(file_id),
            'result': qr_dict if file_id else 'No file_id found'
        })
    except Exception as e:
        debug_info['parsing_attempts'].append({
            'method': 'JSON parsing',
            'success': False,
            'error': str(e)
        })

    # Method 2: Try parsing as Python literal
    if not file_id:
        try:
            import ast
            qr_dict = ast.literal_eval(qr_data)
            file_id = qr_dict.get('file_id')
            debug_info['parsing_attempts'].append({
                'method': 'Python literal parsing',
                'success': bool(file_id),
                'result': qr_dict if file_id else 'No file_id found'
            })
        except Exception as e:
            debug_info['parsing_attempts'].append({
                'method': 'Python literal parsing',
                'success': False,
                'error': str(e)
            })

    # Add more debug info
    debug_info['final_file_id'] = file_id
    debug_info['success'] = bool(file_id)

    return jsonify(debug_info)

@app.route('/api/analytics', methods=['POST'])
def api_analytics():
    """Handle analytics events from the frontend"""
    try:
        data = request.json
        events = data.get('events', [])

        # Log analytics events (you can store these in database if needed)
        for event in events:
            print(f"Analytics Event: {event.get('name')} - {event.get('data')}")

        return jsonify({'success': True, 'processed': len(events)})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/search', methods=['GET'])
@login_required
def api_search():
    """Enhanced search for files by multiple fields including Excel data"""
    try:
        query = request.args.get('q', '').strip()
        if len(query) < 2:
            return jsonify({'results': []})

        # Enhanced search across multiple fields
        files = File.query.filter(
            db.or_(
                File.title.contains(query),
                File.description.contains(query),
                File.file_no.contains(query),
                File.village_name.contains(query),
                File.hobli_name.contains(query),
                File.taluk_name.contains(query),
                File.district_name.contains(query),
                File.survey_no.contains(query),
                File.category.contains(query),
                File.year.contains(query),
                File.subject.contains(query),
                File.bundle_no.contains(query)
            )
        ).limit(20).all()

        results = []
        for file in files:
            # Create enhanced result with more details
            result = {
                'id': file.id,
                'title': file.title,
                'description': file.description,
                'file_no': file.file_no,
                'village_name': file.village_name,
                'hobli_name': file.hobli_name,
                'taluk_name': file.taluk_name,
                'district_name': file.district_name,
                'survey_no': file.survey_no,
                'category': file.category,
                'year': file.year,
                'subject': file.subject,
                'bundle_no': file.bundle_no,
                'location': {
                    'rack': file.location.rack_number if file.location else 'Unknown',
                    'row': file.location.row_number if file.location else 'Unknown',
                    'position': file.location.position if file.location else 'Unknown'
                },
                'url': f'/files/{file.id}'
            }
            results.append(result)

        return jsonify({
            'success': True,
            'results': results,
            'total': len(results),
            'query': query
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/upload_excel', methods=['GET', 'POST'])
@login_required
def upload_excel():
    """Upload and process Excel file to automatically create file records"""
    if request.method == 'POST':
        if 'excel_file' not in request.files:
            flash('No Excel file selected')
            return redirect(request.url)

        excel_file = request.files['excel_file']

        if excel_file.filename == '':
            flash('No file selected')
            return redirect(request.url)

        if excel_file and excel_file.filename.endswith(('.xlsx', '.xls')):
            try:
                # Read Excel file
                df = pd.read_excel(excel_file)

                # Expected columns mapping
                column_mapping = {
                    'IndexID': 'index_id',
                    'RefID': 'ref_id',
                    'FILE_NO': 'file_no',
                    'Category': 'category',
                    'Year': 'year',
                    'DisposalCat': 'disposal_cat',
                    'Createddate': 'created_date',
                    'RowNo': 'row_no',
                    'RackNo': 'rack_no',
                    'RecordRoomSlNo': 'record_room_sl_no',
                    'BundleNo': 'bundle_no',
                    'ClosureDate': 'closure_date',
                    'ReceiptAtRRDate': 'receipt_at_rr_date',
                    'DestructionDate': 'destruction_date',
                    'Subject': 'subject',
                    'dist_name_en': 'district_name',
                    'taluk_name_en': 'taluk_name',
                    'hobli_name_en': 'hobli_name',
                    'village_name_en': 'village_name',
                    'survey_no': 'survey_no'
                }

                # Check if required columns exist
                missing_columns = [col for col in column_mapping.keys() if col not in df.columns]
                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return redirect(request.url)

                created_files = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Extract data from Excel row
                        rack_no = str(row['RackNo']) if pd.notna(row['RackNo']) else '1'
                        row_no = str(row['RowNo']) if pd.notna(row['RowNo']) else '1'
                        file_no = str(row['FILE_NO']) if pd.notna(row['FILE_NO']) else f'FILE_{index+1}'
                        subject = str(row['Subject']) if pd.notna(row['Subject']) else 'No Subject'

                        # Create title from available data
                        title_parts = []
                        if pd.notna(row['FILE_NO']):
                            title_parts.append(f"File: {row['FILE_NO']}")
                        if pd.notna(row['Subject']):
                            title_parts.append(str(row['Subject']))
                        if pd.notna(row['village_name_en']):
                            title_parts.append(f"Village: {row['village_name_en']}")

                        title = ' - '.join(title_parts) if title_parts else f'Excel Import {index+1}'

                        # Create description from additional fields
                        description_parts = []
                        if pd.notna(row['Category']):
                            description_parts.append(f"Category: {row['Category']}")
                        if pd.notna(row['Year']):
                            description_parts.append(f"Year: {row['Year']}")
                        if pd.notna(row['dist_name_en']):
                            description_parts.append(f"District: {row['dist_name_en']}")
                        if pd.notna(row['taluk_name_en']):
                            description_parts.append(f"Taluk: {row['taluk_name_en']}")
                        if pd.notna(row['hobli_name_en']):
                            description_parts.append(f"Hobli: {row['hobli_name_en']}")
                        if pd.notna(row['survey_no']):
                            description_parts.append(f"Survey No: {row['survey_no']}")

                        description = ' | '.join(description_parts) if description_parts else 'Imported from Excel'

                        # Create location record
                        location = Location(
                            rack_number=rack_no,
                            row_number=row_no,
                            position=str(row['RecordRoomSlNo']) if pd.notna(row['RecordRoomSlNo']) else '1'
                        )
                        db.session.add(location)
                        db.session.flush()

                        # Find the appropriate bundle for this file
                        rack_num = int(rack_no) if rack_no.isdigit() else 1
                        bundle_no_str = str(row['BundleNo']) if pd.notna(row['BundleNo']) else '1'

                        # Parse bundle number (handle various formats)
                        import re
                        bundle_match = re.search(r'(\d+)', bundle_no_str)
                        bundle_num = int(bundle_match.group(1)) if bundle_match else 1

                        # Ensure valid ranges
                        if rack_num < 1 or rack_num > 20:
                            rack_num = 1
                        if bundle_num < 1 or bundle_num > 40:
                            bundle_num = 1

                        # Find the rack and bundle
                        rack = Rack.query.filter_by(rack_number=rack_num).first()
                        bundle_id = None
                        if rack:
                            bundle = Bundle.query.filter_by(
                                rack_id=rack.id,
                                bundle_number=bundle_num
                            ).first()
                            if bundle:
                                bundle_id = bundle.id
                                # Update bundle village name if not set
                                village_name = str(row['village_name_en']) if pd.notna(row['village_name_en']) else None
                                if village_name and not bundle.village_name:
                                    bundle.village_name = village_name

                        # Create file record with Excel data
                        new_file = File(
                            title=title,
                            description=description,
                            filename=None,  # No physical file uploaded
                            original_filename=None,
                            location_id=location.id,
                            user_id=current_user.id,
                            bundle_id=bundle_id,
                            # Excel import fields
                            file_no=file_no,
                            bundle_no=bundle_no_str,
                            village_name=str(row['village_name_en']) if pd.notna(row['village_name_en']) else None,
                            hobli_name=str(row['hobli_name_en']) if pd.notna(row['hobli_name_en']) else None,
                            taluk_name=str(row['taluk_name_en']) if pd.notna(row['taluk_name_en']) else None,
                            district_name=str(row['dist_name_en']) if pd.notna(row['dist_name_en']) else None,
                            survey_no=str(row['survey_no']) if pd.notna(row['survey_no']) else None,
                            category=str(row['Category']) if pd.notna(row['Category']) else None,
                            year=str(row['Year']) if pd.notna(row['Year']) else None,
                            subject=str(row['Subject']) if pd.notna(row['Subject']) else None
                        )
                        db.session.add(new_file)
                        db.session.flush()

                        # Generate QR code
                        qr_data = {
                            'file_id': new_file.id,
                            'title': title,
                            'file_no': file_no,
                            'location': f"Rack: {rack_no}, Row: {row_no}",
                            'url': f"http://127.0.0.1:5001/files/{new_file.id}",
                            'type': 'T-Office-File'
                        }

                        qr = qrcode.QRCode(
                            version=1,
                            error_correction=qrcode.constants.ERROR_CORRECT_M,
                            box_size=10,
                            border=4,
                        )

                        qr.add_data(json.dumps(qr_data))
                        qr.make(fit=True)

                        qr_img = qr.make_image(fill_color="black", back_color="white")
                        qr_filename = f"qr_{new_file.id}.png"
                        qr_path = os.path.join(app.config['QR_CODE_FOLDER'], qr_filename)
                        qr_img.save(qr_path)

                        # Update file with QR code path
                        new_file.qr_code = qr_filename

                        # Log the file creation
                        access_log = AccessLog(
                            file_id=new_file.id,
                            user_id=current_user.id,
                            action="created_from_excel"
                        )
                        db.session.add(access_log)

                        created_files += 1

                    except Exception as e:
                        # Rollback the current transaction for this row
                        db.session.rollback()
                        errors.append(f"Row {index+1}: {str(e)}")
                        continue

                # Commit all changes
                try:
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    flash(f'Database error: {str(e)}')
                    return redirect(request.url)

                if created_files > 0:
                    flash(f'Successfully created {created_files} file records from Excel')

                    # Emit real-time update to all connected clients
                    socketio.emit('files_updated', {
                        'message': f'{created_files} new files added from Excel upload',
                        'count': created_files,
                        'type': 'excel_upload',
                        'user': current_user.username
                    }, broadcast=True)

                if errors:
                    flash(f'Errors encountered: {"; ".join(errors[:5])}')  # Show first 5 errors

                return redirect(url_for('dashboard'))

            except Exception as e:
                flash(f'Error processing Excel file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Please upload a valid Excel file (.xlsx or .xls)')
            return redirect(request.url)

    return render_template('upload_excel.html')

@app.route('/upload_excel_enhanced', methods=['GET', 'POST'])
@login_required
def upload_excel_enhanced():
    """Enhanced Excel upload with improved database storage and rack organization"""
    if request.method == 'POST':
        if 'excel_file' not in request.files:
            flash('No Excel file selected', 'error')
            return redirect(request.url)

        excel_file = request.files['excel_file']

        if excel_file.filename == '':
            flash('No file selected', 'error')
            return redirect(request.url)

        if excel_file and excel_file.filename.endswith(('.xlsx', '.xls')):
            try:
                # Read Excel file
                df = pd.read_excel(excel_file)

                # Log upload attempt
                print(f"📊 Processing Excel file: {excel_file.filename}")
                print(f"📈 Total rows: {len(df)}")
                print(f"📋 Columns: {list(df.columns)}")

                # Expected columns
                required_columns = [
                    'IndexID', 'RefID', 'FILE_NO', 'Category', 'Year', 'DisposalCat',
                    'Createddate', 'RowNo', 'RackNo', 'RecordRoomSlNo', 'BundleNo',
                    'ClosureDate', 'ReceiptAtRRDate', 'DestructionDate', 'Subject',
                    'dist_name_en', 'taluk_name_en', 'hobli_name_en', 'village_name_en', 'survey_no'
                ]

                # Check if Excel has the required columns
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    flash(f'Excel file missing required columns: {", ".join(missing_columns)}', 'error')
                    return redirect(request.url)

                created_files = 0
                updated_files = 0
                errors = []
                rack_stats = {}
                bundle_stats = {}

                # Validate row indices
                if df.index.duplicated().any():
                    flash('❌ Excel file contains duplicate row indices', 'error')
                    return redirect(request.url)

                # Process each row in the Excel file
                for index, row in df.iterrows():
                    try:
                        # Validate row data
                        if pd.isna(row).all():
                            print(f"⚠️ Skipping empty row {index}")
                            continue
                        # Validate row data
                        if pd.isna(row).all():
                            print(f"⚠️ Skipping empty row {index}")
                            continue
                        file_no = str(row['FILE_NO']).strip() if pd.notna(row['FILE_NO']) else f"F-{index+1:04d}"
                        subject = str(row['Subject']).strip() if pd.notna(row['Subject']) else "No Subject"
                        village_name = str(row['village_name_en']).strip() if pd.notna(row['village_name_en']) else "Unknown Village"

                        # Create comprehensive title
                        title = f"{file_no} - {subject[:80]} ({village_name})"

                        # Create detailed description
                        category = str(row['Category']).strip() if pd.notna(row['Category']) else "General"
                        year = str(row['Year']).strip() if pd.notna(row['Year']) else "Unknown"
                        district = str(row['dist_name_en']).strip() if pd.notna(row['dist_name_en']) else "Unknown District"
                        taluk = str(row['taluk_name_en']).strip() if pd.notna(row['taluk_name_en']) else "Unknown Taluk"
                        hobli = str(row['hobli_name_en']).strip() if pd.notna(row['hobli_name_en']) else "Unknown Hobli"
                        survey_no = str(row['survey_no']).strip() if pd.notna(row['survey_no']) else "N/A"

                        description = f"📁 Category: {category} | 📅 Year: {year} | 🏛️ District: {district} | 🏘️ Taluk: {taluk} | 🌾 Hobli: {hobli} | 📍 Survey No: {survey_no}"

                        # Extract and validate location data
                        rack_no = int(row['RackNo']) if pd.notna(row['RackNo']) and str(row['RackNo']).replace('.', '').isdigit() else 1
                        row_no = str(row['RowNo']).strip() if pd.notna(row['RowNo']) else "1"
                        record_room_sl_no = str(row['RecordRoomSlNo']).strip() if pd.notna(row['RecordRoomSlNo']) else "1"
                        bundle_no_str = str(row['BundleNo']).strip() if pd.notna(row['BundleNo']) else "1"
                        bundle_no = int(bundle_no_str) if bundle_no_str.replace('.', '').isdigit() else 1

                        # Ensure valid ranges for rack and bundle
                        if rack_no < 1 or rack_no > 20:
                            rack_no = ((rack_no - 1) % 20) + 1  # Wrap around to valid range
                        if bundle_no < 1 or bundle_no > 40:
                            bundle_no = ((bundle_no - 1) % 40) + 1  # Wrap around to valid range

                        # Track statistics
                        rack_stats[rack_no] = rack_stats.get(rack_no, 0) + 1
                        bundle_key = f"R{rack_no}B{bundle_no}"
                        bundle_stats[bundle_key] = bundle_stats.get(bundle_key, 0) + 1

                        # Check if file already exists (avoid duplicates)
                        existing_file = File.query.filter_by(file_no=file_no).first()
                        if existing_file:
                            # Update existing file
                            existing_file.title = title
                            existing_file.description = description
                            existing_file.village_name = village_name
                            existing_file.hobli_name = hobli
                            existing_file.taluk_name = taluk
                            existing_file.district_name = district
                            existing_file.survey_no = survey_no
                            existing_file.category = category
                            existing_file.year = year
                            existing_file.subject = subject
                            updated_files += 1
                            continue

                        # Create location record
                        location = Location(
                            rack_number=str(rack_no),
                            row_number=row_no,
                            position=record_room_sl_no
                        )
                        db.session.add(location)
                        db.session.flush()  # Get the location ID

                        # Find or create rack
                        rack = Rack.query.filter_by(rack_number=rack_no).first()
                        if not rack:
                            rack = Rack(
                                rack_number=rack_no,
                                name=f"Rack {rack_no}",
                                description=f"Government Document Storage Rack {rack_no} - Auto-created from Excel import"
                            )
                            db.session.add(rack)
                            db.session.flush()

                        # Find or create bundle
                        bundle = Bundle.query.filter_by(
                            rack_id=rack.id,
                            bundle_number=bundle_no
                        ).first()

                        if not bundle:
                            bundle = Bundle(
                                bundle_number=bundle_no,
                                rack_id=rack.id,
                                name=f"Bundle {bundle_no} - {village_name}",
                                description=f"Document Bundle {bundle_no} in Rack {rack_no} for {village_name}",
                                village_name=village_name
                            )
                            db.session.add(bundle)
                            db.session.flush()
                        else:
                            # Update bundle village if not set
                            if not bundle.village_name:
                                bundle.village_name = village_name

                        # Create comprehensive file record
                        new_file = File(
                            title=title,
                            description=description,
                            filename=None,  # No physical file uploaded
                            original_filename=None,
                            location_id=location.id,
                            user_id=current_user.id,
                            bundle_id=bundle.id,
                            # Excel import fields
                            file_no=file_no,
                            bundle_no=bundle_no_str,
                            village_name=village_name,
                            hobli_name=hobli,
                            taluk_name=taluk,
                            district_name=district,
                            survey_no=survey_no,
                            category=category,
                            year=year,
                            subject=subject
                        )
                        db.session.add(new_file)
                        db.session.flush()  # Get the file ID

                        # Generate enhanced QR code with more data
                        qr_data = {
                            'file_id': new_file.id,
                            'title': title,
                            'file_no': file_no,
                            'location': f"Rack {rack_no}, Bundle {bundle_no}, Row {row_no}",
                            'village': village_name,
                            'hobli': hobli,
                            'taluk': taluk,
                            'district': district,
                            'survey_no': survey_no,
                            'category': category,
                            'year': year,
                            'url': f"http://127.0.0.1:5001/files/{new_file.id}",
                            'type': 'T-Office-File',
                            'created_by': current_user.username,
                            'import_date': datetime.now(timezone.utc).isoformat()
                        }

                        # Create QR code
                        qr = qrcode.QRCode(version=1, box_size=10, border=5)
                        qr.add_data(json.dumps(qr_data))
                        qr.make(fit=True)

                        qr_img = qr.make_image(fill_color="black", back_color="white")
                        qr_filename = f"qr_{new_file.id}.png"
                        qr_path = os.path.join(app.config['QR_CODE_FOLDER'], qr_filename)
                        qr_img.save(qr_path)

                        # Update file with QR code path
                        new_file.qr_code = qr_filename

                        # Log the file creation
                        access_log = AccessLog(
                            file_id=new_file.id,
                            user_id=current_user.id,
                            action="created_from_excel",
                            details=f"Imported from {excel_file.filename}"
                        )
                        db.session.add(access_log)

                        created_files += 1

                        # Progress indicator for large files
                        if (index + 1) % 10 == 0:
                            print(f"📈 Processed {index + 1}/{len(df)} rows...")

                    except Exception as e:
                        # Log error but continue processing
                        error_msg = f"Row {index+1} (File: {file_no if 'file_no' in locals() else 'Unknown'}): {str(e)}"
                        errors.append(error_msg)
                        print(f"❌ Error: {error_msg}")
                        continue

                # Commit all changes
                try:
                    db.session.commit()
                except Exception as commit_error:
                    db.session.rollback()
                    error_msg = f"Database commit failed: {str(commit_error)}"
                    flash(error_msg, 'error')
                    print(f"❌ {error_msg}")
                    errors.append(error_msg)
                    return redirect(request.url)

                    # Success message with statistics 
                    success_msg = f'✅ Excel import completed successfully!'
                    if created_files > 0:
                        success_msg += f' Created {created_files} new file records.'
                    if updated_files > 0:
                        success_msg += f' Updated {updated_files} existing files.'

                    flash(success_msg, 'success')

                    # Additional statistics
                    rack_count = len(rack_stats)
                    bundle_count = len(bundle_stats)
                    flash(f'📊 Distribution: {rack_count} racks, {bundle_count} bundles used', 'info')

                    if errors:
                        error_count = len(errors)
                        flash(f'⚠️ {error_count} rows had errors and were skipped', 'warning')
                        # Log detailed errors for admin review
                        print(f"❌ Errors encountered during import:")
                        for error in errors[:10]:  # Show first 10 errors
                            print(f"   {error}")

                except Exception as e:
                    db.session.rollback()
                    flash(f'❌ Database error during commit: {str(e)}', 'error')
                    return redirect(request.url)

                return redirect(url_for('dashboard'))

            except pd.errors.EmptyDataError:
                flash('❌ The Excel file is empty', 'error')
                return redirect(request.url)
            except pd.errors.ParserError:
                flash('❌ Invalid Excel file format', 'error')
                return redirect(request.url)
            except Exception as e:
                error_msg = str(e)
                flash(f'❌ Error processing Excel file: {error_msg}', 'error')
                print(f"❌ Excel processing error: {error_msg}")
                # Log detailed error for debugging
                logging.error(f"Excel processing error: {error_msg}", exc_info=True)
                return redirect(request.url)
        else:
            flash('Please upload a valid Excel file (.xlsx or .xls)', 'error')
            return redirect(request.url)

    return render_template('upload_excel.html')

@app.route('/search')
@login_required
def search_page():
    """Dedicated search page for finding files"""
    # Get filter options from database
    hoblis = File.query.with_entities(File.hobli_name).distinct().all()
    villages = File.query.with_entities(File.village_name).distinct().all()
    categories = File.query.with_entities(File.category).distinct().all()
    years = File.query.with_entities(File.year).distinct().all()

    return render_template('search.html',  
        hoblis=[h[0] for h in hoblis if h[0]],
        villages=[v[0] for v in villages if v[0]], 
        categories=[c[0] for c in categories if c[0]],
        years=[y[0] for y in years if y[0]])

@app.route('/api/search/advanced', methods=['POST'])
@login_required
def advanced_search():
    """Advanced search with multiple criteria"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No search criteria provided'})

        # Build query based on provided criteria
        query = File.query.join(Location)

        if data.get('file_no'):
            query = query.filter(File.file_no.contains(data['file_no']))

        if data.get('village_name'):
            query = query.filter(File.village_name.contains(data['village_name']))

        if data.get('hobli_name'):
            query = query.filter(File.hobli_name.contains(data['hobli_name']))

        if data.get('taluk_name'):
            query = query.filter(File.taluk_name.contains(data['taluk_name']))

        if data.get('district_name'):
            query = query.filter(File.district_name.contains(data['district_name']))

        if data.get('survey_no'):
            query = query.filter(File.survey_no.contains(data['survey_no']))

        if data.get('category'):
            query = query.filter(File.category.contains(data['category']))

        if data.get('year'):
            query = query.filter(File.year.contains(data['year']))

        if data.get('rack_number'):
            query = query.join(Location).filter(Location.rack_number.contains(data['rack_number']))

        if data.get('bundle_no'):
            query = query.filter(File.bundle_no.contains(data['bundle_no']))

        # Add sorting
        sort_by = data.get('sort_by', 'year')
        sort_order = data.get('sort_order', 'desc')
        
        if sort_by == 'year':
            query = query.order_by(File.year.desc() if sort_order == 'desc' else File.year.asc())
        elif sort_by == 'title':
            query = query.order_by(File.title.desc() if sort_order == 'desc' else File.title.asc())
        
        # Execute query with pagination
        page = data.get('page', 1)
        per_page = data.get('per_page', 50)
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        files = pagination.items

        results = []
        for file in files:
            result = {
                'id': file.id,
                'title': file.title,
                'file_no': file.file_no,
                'village_name': file.village_name,
                'hobli_name': file.hobli_name,
                'taluk_name': file.taluk_name,
                'district_name': file.district_name,
                'survey_no': file.survey_no,
                'category': file.category,
                'year': file.year,
                'subject': file.subject,
                'bundle_no': file.bundle_no,
                'location': {
                    'rack': file.location.rack_number if file.location else 'Unknown',
                    'row': file.location.row_number if file.location else 'Unknown',
                    'position': file.location.position if file.location else 'Unknown'
                },
                'url': f'/files/{file.id}'
            }
            results.append(result)

        return jsonify({
            'success': True,
            'results': results,
            'total': len(results)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stats')
@login_required
def api_stats():
    """API endpoint to get dashboard statistics for real-time updates"""
    files = File.query.all()
    total_files = len(files)
    files_with_access_logs = len([f for f in files if f.access_logs.count() > 0])
    files_with_qr_codes = len([f for f in files if f.qr_code])
    files_from_excel = len([f for f in files if not f.filename])
    files_uploaded = len([f for f in files if f.filename])

    stats = {
        'total_files': total_files,
        'processed_files': files_with_access_logs,
        'qr_codes_generated': files_with_qr_codes,
        'excel_imports': files_from_excel,
        'manual_uploads': files_uploaded
    }
    return jsonify(stats)

@app.route('/analytics')
@login_required
def analytics():
    # Get file access statistics
    file_stats = db.session.query(
        File.title,
        db.func.count(AccessLog.id).label('access_count')
    ).join(AccessLog).group_by(File.id).all()

    # Get user access statistics
    user_stats = db.session.query(
        User.username,
        db.func.count(AccessLog.id).label('access_count')
    ).join(AccessLog).group_by(User.id).all()

    return render_template('analytics.html', file_stats=file_stats, user_stats=user_stats)

# Hierarchical File Browsing Routes
@app.route('/files/browse')
@login_required
def browse_files():
    """Main file browsing page showing all racks"""
    # Get all racks (1-20) with their file counts using the new Rack model
    racks_data = []

    for rack_num in range(1, 21):  # Racks 1-20
        rack = Rack.query.filter_by(rack_number=rack_num).first()
        if rack:
            file_count = rack.file_count
            bundle_count = rack.bundle_count
            racks_data.append({
                'rack_number': rack_num,
                'file_count': file_count,
                'bundle_count': bundle_count,
                'name': rack.name or f'Rack {rack_num}'
            })
        else:
            # Create empty rack entry for display
            racks_data.append({
                'rack_number': rack_num,
                'file_count': 0,
                'bundle_count': 0,
                'name': f'Rack {rack_num}'
            })

    return render_template('browse_files.html', racks=racks_data)

@app.route('/api/racks')
@login_required
def api_racks():
    """API endpoint to get racks data for real-time updates"""
    racks_data = []

    for rack_num in range(1, 21):  # Racks 1-20
        rack = Rack.query.filter_by(rack_number=rack_num).first()
        if rack:
            file_count = rack.file_count
            bundle_count = rack.bundle_count
            racks_data.append({
                'rack_number': rack_num,
                'file_count': file_count,
                'bundle_count': bundle_count,
                'name': rack.name or f'Rack {rack_num}'
            })
        else:
            racks_data.append({
                'rack_number': rack_num,
                'file_count': 0,
                'bundle_count': 0,
                'name': f'Rack {rack_num}'
            })

    return jsonify({'racks': racks_data})

@app.route('/files/rack/<rack_number>')
@login_required
def browse_rack(rack_number):
    """Show bundles in a specific rack"""
    try:
        rack_num = int(rack_number)
    except ValueError:
        flash('Invalid rack number', 'error')
        return redirect(url_for('browse_files'))

    # Get the rack
    rack = Rack.query.filter_by(rack_number=rack_num).first()
    if not rack:
        flash(f'Rack {rack_number} not found', 'error')
        return redirect(url_for('browse_files'))

    # Get all bundles in this rack (1-40) with their file counts, sorted by bundle number
    bundles_data = []

    for bundle_num in range(1, 41):  # Bundles 1-40
        bundle = Bundle.query.filter_by(
            rack_id=rack.id,
            bundle_number=bundle_num
        ).first()

        if bundle:
            file_count = bundle.file_count
            if file_count > 0:  # Only show bundles with files
                bundles_data.append({
                    'bundle_no': bundle_num,
                    'file_count': file_count,
                    'village_name': bundle.village_name,
                    'name': bundle.name or f'Bundle {bundle_num}',
                    'description': bundle.description
                })

    return render_template('browse_rack.html',
                         rack_number=rack_number,
                         rack=rack,
                         bundles=bundles_data)

@app.route('/files/rack/<rack_number>/bundle/<bundle_number>')
@login_required
def browse_bundle(rack_number, bundle_number):
    """Show files in a specific bundle"""
    try:
        rack_num = int(rack_number)
        bundle_num = int(bundle_number)
    except ValueError:
        flash('Invalid rack or bundle number', 'error')
        return redirect(url_for('browse_files'))

    # Get the rack
    rack = Rack.query.filter_by(rack_number=rack_num).first()
    if not rack:
        flash(f'Rack {rack_number} not found', 'error')
        return redirect(url_for('browse_files'))

    # Get the bundle
    bundle = Bundle.query.filter_by(
        rack_id=rack.id,
        bundle_number=bundle_num
    ).first()

    if not bundle:
        flash(f'Bundle {bundle_number} not found in Rack {rack_number}', 'error')
        return redirect(url_for('browse_rack', rack_number=rack_number))

    # Get all files in this bundle, sorted by file_no
    files = File.query.filter_by(bundle_id=bundle.id).order_by(File.file_no).all()

    return render_template('browse_bundle.html',
                         rack_number=rack_number,
                         bundle_number=bundle_number,
                         rack=rack,
                         bundle=bundle,
                         files=files)

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('500.html'), 500

# SocketIO already initialized above

# Add this route
@app.route('/collaboration')
@login_required
def collaboration():
    # Get active users and their recent activities
    active_logs = AccessLog.query.filter(
        AccessLog.timestamp >= datetime.now(timezone.utc) - timedelta(hours=1)
    ).order_by(AccessLog.timestamp.desc()).limit(20).all()

    return render_template('collaboration.html', active_logs=active_logs)

# Add this socket event
@socketio.on('file_access')
def handle_file_access(data):
    # Broadcast to all connected clients
    emit('file_activity', {
        'user': current_user.username,
        'file_id': data['file_id'],
        'file_title': data['file_title'],
        'action': data['action'],
        'timestamp': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
    }, broadcast=True)

# Hierarchical Search API endpoints
@app.route('/api/get-hoblis')
def get_hoblis():
    """Get all unique hoblis from the database"""
    try:
        hoblis = db.session.query(File.hobli_name).filter(
            File.hobli_name.isnot(None),
            File.hobli_name != ''
        ).distinct().order_by(File.hobli_name).all()

        hobli_list = [hobli[0] for hobli in hoblis if hobli[0]]

        return jsonify({
            'status': 'success',
            'hoblis': hobli_list
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/get-villages')
def get_villages():
    """Get villages for a specific hobli"""
    try:
        hobli = request.args.get('hobli')
        if not hobli:
            return jsonify({
                'status': 'error',
                'message': 'Hobli parameter is required'
            }), 400

        villages = db.session.query(File.village_name).filter(
            File.hobli_name == hobli,
            File.village_name.isnot(None),
            File.village_name != ''
        ).distinct().order_by(File.village_name).all()

        village_list = [village[0] for village in villages if village[0]]

        return jsonify({
            'status': 'success',
            'villages': village_list
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# Survey numbers are now entered manually, so this endpoint is not needed

@app.route('/api/search-by-location')
def search_by_location():
    """Search files by hobli, village, and survey number"""
    try:
        hobli = request.args.get('hobli')
        village = request.args.get('village')
        survey_no = request.args.get('survey_no')

        if not hobli or not village or not survey_no:
            return jsonify({
                'status': 'error',
                'message': 'Hobli, village, and survey_no parameters are required'
            }), 400

        # Search for files matching the criteria
        files = db.session.query(File).join(Location).filter(
            File.hobli_name == hobli,
            File.village_name == village,
            File.survey_no == survey_no
        ).all()

        # Format the results
        file_results = []
        for file in files:
            # Get rack and bundle information
            rack_number = file.location.rack_number if file.location else 'Unknown'
            bundle_number = file.bundle_no if file.bundle_no else 'Unknown'

            file_results.append({
                'id': file.id,
                'title': file.title,
                'village_name': file.village_name,
                'hobli_name': file.hobli_name,
                'survey_no': file.survey_no,
                'rack_number': rack_number,
                'bundle_number': bundle_number,
                'file_no': file.file_no,
                'description': file.description
            })

        return jsonify({
            'status': 'success',
            'files': file_results,
            'count': len(file_results)
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

if __name__ == '__main__':
    # Create all database tables and default users
    with app.app_context():
        db.create_all()

        # Create default users if they don't exist
        if not User.query.filter_by(username='admin').first():
            admin_user = User(username='admin', email='<EMAIL>', role='Administrator')
            admin_user.set_password('admin123')
            db.session.add(admin_user)

        if not User.query.filter_by(username='officer').first():
            officer_user = User(username='officer', email='<EMAIL>', role='Officer')
            officer_user.set_password('officer123')
            db.session.add(officer_user)

        if not User.query.filter_by(username='clerk').first():
            clerk_user = User(username='clerk', email='<EMAIL>', role='Clerk')
            clerk_user.set_password('clerk123')
            db.session.add(clerk_user)

        db.session.commit()
        print("Database initialized with default users:")
        print("Administrator: admin/admin123")
        print("Officer: officer/officer123")
        print("Clerk: clerk/clerk123")

    app.run(debug=True, port=5001)